// ==UserScript==
// @name         Script Teste
// @namespace    script-test
// @version      1.0.0
// <AUTHOR>
// @description  Um script de teste para demonstrar funcionalidades avançadas do Tampermonkey
// @license MIT
// @match        https://github.com/*
// @match        https://www.google.com/*
// @grant        none
// @run-at       document-idle
// ==/UserScript==

// ===== CONFIGURAÇÕES =====
const CONFIG = {
	SCRIPT_NAME: "teste",
	VERSION: "1.0.30",
	STORAGE_KEYS: {
		SETTINGS: "settings",
	},
} as const;

// ===== SERVIÇOS DE ARMAZENAMENTO ESSENCIAL =====
class StorageService {
	static get<T>(key: string): T | null {
		try {
			const item = localStorage.getItem(key);
			return item ? JSON.parse(item) : null;
		} catch {
			return null;
		}
	}
	static setJSON<T>(key: string, value: T): boolean {
		try {
			localStorage.setItem(key, JSON.stringify(value));
			return true;
		} catch {
			return false;
		}
	}
	static remove(key: string): boolean {
		try {
			localStorage.removeItem(key);
			return true;
		} catch {
			return false;
		}
	}
}

// ===== UTILITÁRIOS DOM ESSENCIAL =====
interface ElementOptions {
	className?: string;
	textContent?: string;
	styles?: Partial<CSSStyleDeclaration>;
	attributes?: Record<string, string>;
	eventListeners?: Record<string, EventListener>;
}
class DOMUtilities {
	static createElement<T extends HTMLElement>(tag: string, options: ElementOptions = {}): T {
		const element = document.createElement(tag) as T;
		if (options.className) element.className = options.className;
		if (options.textContent) element.textContent = options.textContent;
		if (options.styles) Object.assign(element.style, options.styles);
		if (options.attributes)
			Object.entries(options.attributes).forEach(([key, value]) => {
				element.setAttribute(key, value);
			});
		if (options.eventListeners)
			Object.entries(options.eventListeners).forEach(([event, listener]) => {
				element.addEventListener(event, listener);
			});
		return element;
	}
	static removeElement(element: HTMLElement | null): boolean {
		if (element && element.parentNode) {
			element.parentNode.removeChild(element);
			return true;
		}
		return false;
	}
}

// ===== GERENCIAMENTO DE ESTADO ESSENCIAL =====
type SettingsValue = string | number | boolean | object | null;
class SettingsStore {
	private static instance: SettingsStore;
	private settings: Record<string, SettingsValue> = {};
	private readonly storageKey = CONFIG.STORAGE_KEYS.SETTINGS;

	private constructor() {
		this.loadSettings();
	}

	static getInstance(): SettingsStore {
		if (!SettingsStore.instance) {
			SettingsStore.instance = new SettingsStore();
		}
		return SettingsStore.instance;
	}

	private loadSettings(): void {
		const savedSettings = StorageService.get<Record<string, SettingsValue>>(this.storageKey);
		if (savedSettings && typeof savedSettings === "object") {
			this.settings = savedSettings;
		}
	}

	private saveSettings(): boolean {
		return StorageService.setJSON(this.storageKey, this.settings);
	}

	getAllSettings(): Record<string, SettingsValue> {
		return { ...this.settings };
	}

	getSetting<T extends SettingsValue>(key: string): T | null {
		return (this.settings[key] as T) || null;
	}

	setSetting(key: string, value: SettingsValue): boolean {
		this.settings[key] = value;
		return this.saveSettings();
	}
}

// ===== GERENCIADOR DE KEYBINDS ESSENCIAL =====
type KeyHandler = (event: KeyboardEvent) => void | boolean;
interface KeyBinding {
	key: string;
	ctrl?: boolean;
	shift?: boolean;
	alt?: boolean;
	handler: KeyHandler;
	description?: string;
}
class KeyBindManager {
	private static instance: KeyBindManager;
	private keyBindings: Map<string, KeyBinding> = new Map();

	private constructor() {
		this.setupGlobalListener();
	}

	static getInstance(): KeyBindManager {
		if (!KeyBindManager.instance) {
			KeyBindManager.instance = new KeyBindManager();
		}
		return KeyBindManager.instance;
	}

	private setupGlobalListener(): void {
		document.addEventListener("keydown", event => {
			const key = event.key.toLowerCase();
			const binding = this.keyBindings.get(key);
			if (binding) {
				const result = binding.handler(event);
				if (result !== false) {
					event.preventDefault();
					event.stopPropagation();
				}
			}
		});
	}

	register(binding: KeyBinding): boolean {
		const key = binding.key.toLowerCase();
		this.keyBindings.set(key, binding);
		return true;
	}

	listBindings(): KeyBinding[] {
		return Array.from(this.keyBindings.values());
	}
}

// ===== GERENCIADOR DE UI ESSENCIAL =====
interface UIComponent {
	render(): HTMLElement;
	destroy(): void;
}
class UIManager {
	private static instance: UIManager;
	private readonly keyBindManager: KeyBindManager;
	private isInitialized = false;

	private constructor() {
		this.keyBindManager = KeyBindManager.getInstance();
	}

	static getInstance(): UIManager {
		if (!UIManager.instance) UIManager.instance = new UIManager();
		return UIManager.instance;
	}

	initialize(): boolean {
		if (this.isInitialized) return true;
		this.keyBindManager.register({
			key: "F1",
			handler: () => alert("Ajuda"),
			description: "Mostra a ajuda",
		});
		this.isInitialized = true;
		return true;
	}
	destroy(): void {
		this.isInitialized = false;
	}
}

// ===== APLICAÇÃO PRINCIPAL ESSENCIAL =====
class ScriptApplication {
	private static instance: ScriptApplication;
	private readonly settingsStore: SettingsStore;
	private readonly uiManager: UIManager;
	private isInitialized = false;

	private constructor() {
		this.settingsStore = SettingsStore.getInstance();
		this.uiManager = UIManager.getInstance();
	}

	static getInstance(): ScriptApplication {
		if (!ScriptApplication.instance) ScriptApplication.instance = new ScriptApplication();
		return ScriptApplication.instance;
	}

	async initialize(): Promise<boolean> {
		if (this.isInitialized) return true;
		if (document.readyState === "loading") await new Promise(resolve => document.addEventListener("DOMContentLoaded", resolve));
		this.uiManager.initialize();
		this.settingsStore.setSetting("appVersion", CONFIG.VERSION);
		this.isInitialized = true;
		return true;
	}
}

// ===== INICIALIZAÇÃO AUTOMÁTICA =====
ScriptApplication.getInstance().initialize();
