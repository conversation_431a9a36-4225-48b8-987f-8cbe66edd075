// ==UserScript==
// @name         Script Teste
// @namespace    script-test
// @version      1.0.0
// <AUTHOR>
// @description  Um script de teste para demonstrar funcionalidades avançadas do Tampermonkey
// @license MIT
// @match        https://agariobr.com.br/*
// @grant        none
// @run-at       document-idle
// ==/UserScript==
// ===== CONFIGURAÇÕES =====

"use strict";

const CONFIG = {
	SCRIPT_NAME: "teste",
	VERSION: "1.0.30",
	STORAGE_KEYS: {
		SETTINGS: "settings",
	},
};
// ===== SERVIÇOS DE ARMAZENAMENTO ESSENCIAL =====
class StorageService {
	static get(key) {
		try {
			const item = localStorage.getItem(key);
			return item ? JSON.parse(item) : null;
		} catch (_a) {
			return null;
		}
	}
	static setJSON(key, value) {
		try {
			localStorage.setItem(key, JSON.stringify(value));
			return true;
		} catch (_a) {
			return false;
		}
	}
	static remove(key) {
		try {
			localStorage.removeItem(key);
			return true;
		} catch (_a) {
			return false;
		}
	}
}
class DOMUtilities {
	static createElement(tag, options = {}) {
		const element = document.createElement(tag);
		if (options.className) element.className = options.className;
		if (options.textContent) element.textContent = options.textContent;
		if (options.styles) Object.assign(element.style, options.styles);
		if (options.attributes)
			Object.entries(options.attributes).forEach(([key, value]) => {
				element.setAttribute(key, value);
			});
		if (options.eventListeners)
			Object.entries(options.eventListeners).forEach(([event, listener]) => {
				element.addEventListener(event, listener);
			});
		return element;
	}
	static removeElement(element) {
		if (element && element.parentNode) {
			element.parentNode.removeChild(element);
			return true;
		}
		return false;
	}
}
class SettingsStore {
	constructor() {
		this.settings = {};
		this.storageKey = CONFIG.STORAGE_KEYS.SETTINGS;
		this.loadSettings();
	}
	static getInstance() {
		if (!SettingsStore.instance) {
			SettingsStore.instance = new SettingsStore();
		}
		return SettingsStore.instance;
	}
	loadSettings() {
		const savedSettings = StorageService.get(this.storageKey);
		if (savedSettings && typeof savedSettings === "object") {
			this.settings = savedSettings;
		}
	}
	saveSettings() {
		return StorageService.setJSON(this.storageKey, this.settings);
	}
	getAllSettings() {
		return Object.assign({}, this.settings);
	}
	getSetting(key) {
		return this.settings[key] || null;
	}
	setSetting(key, value) {
		this.settings[key] = value;
		return this.saveSettings();
	}
}
class KeyBindManager {
	constructor() {
		this.keyBindings = new Map();
		this.setupGlobalListener();
	}
	static getInstance() {
		if (!KeyBindManager.instance) {
			KeyBindManager.instance = new KeyBindManager();
		}
		return KeyBindManager.instance;
	}
	setupGlobalListener() {
		document.addEventListener("keydown", event => {
			const key = event.key.toLowerCase();
			const binding = this.keyBindings.get(key);
			if (binding) {
				const result = binding.handler(event);
				if (result !== false) {
					event.preventDefault();
					event.stopPropagation();
				}
			}
		});
	}
	register(binding) {
		const key = binding.key.toLowerCase();
		this.keyBindings.set(key, binding);
		return true;
	}
	listBindings() {
		return Array.from(this.keyBindings.values());
	}
}
class UIManager {
	constructor() {
		this.isInitialized = false;
		this.keyBindManager = KeyBindManager.getInstance();
	}
	static getInstance() {
		if (!UIManager.instance) UIManager.instance = new UIManager();
		return UIManager.instance;
	}
	initialize() {
		if (this.isInitialized) return true;
		this.keyBindManager.register({
			key: "F1",
			handler: () => alert("Ajuda"),
			description: "Mostra a ajuda",
		});
		this.isInitialized = true;
		return true;
	}
	destroy() {
		this.isInitialized = false;
	}
}
// ===== APLICAÇÃO PRINCIPAL ESSENCIAL =====
class ScriptApplication {
	constructor() {
		this.isInitialized = false;
		this.settingsStore = SettingsStore.getInstance();
		this.uiManager = UIManager.getInstance();
	}
	static getInstance() {
		if (!ScriptApplication.instance) ScriptApplication.instance = new ScriptApplication();
		return ScriptApplication.instance;
	}
	async initialize() {
		if (this.isInitialized) return true;
		if (document.readyState === "loading") await new Promise(resolve => document.addEventListener("DOMContentLoaded", resolve));
		this.uiManager.initialize();
		this.settingsStore.setSetting("appVersion", CONFIG.VERSION);
		this.isInitialized = true;
		return true;
	}
}
// ===== INICIALIZAÇÃO AUTOMÁTICA =====
ScriptApplication.getInstance().initialize();
